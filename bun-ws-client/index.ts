console.log("Hello via Bun!");
const WS_URL = "wss://ws.kraken.com/v2";
const socket = new WebSocket(WS_URL);
socket.ping();

// message is received
socket.addEventListener("message", (event) => {
  console.log("Received message:", event.data);
});

// socket opened
socket.addEventListener("open", (event) => {
  console.log("Connected to Kraken WebSocket");
});

// socket closed
socket.addEventListener("close", (event) => {
  console.log("WebSocket closed");
});

// error handler
socket.addEventListener("error", (event) => {
  console.error("WebSocket error:", event);
});
