---
name: Build an HTTP server using <PERSON><PERSON> and <PERSON><PERSON>
---

[<PERSON><PERSON>](https://github.com/honojs/hono) is a lightweight ultrafast web framework designed for the edge.

```ts
import { Hono } from "hono";
const app = new Hono();

app.get("/", c => c.text("Hono!"));

export default app;
```

---

Use `create-hono` to get started with one of <PERSON><PERSON>'s project templates. Select `bun` when prompted for a template.

```sh
$ bun create hono myapp
✔ Which template do you want to use? › bun
cloned honojs/starter#main to /path/to/myapp
✔ Copied project files
$ cd myapp
$ bun install
```

---

Then start the dev server and visit [localhost:3000](http://localhost:3000).

```sh
$ bun run dev
```

---

Refer to <PERSON><PERSON>'s guide on [getting started with Bun](https://hono.dev/getting-started/bun) for more information.
