---
name: Import a JSON file
---

B<PERSON> natively supports `.json` imports.

```json#package.json
{
  "name": "bun",
  "version": "1.0.0",
  "author": {
    "name": "<PERSON>",
    "email": "<EMAIL>"
  }
}
```

---

Import the file like any other source file.

```ts
import data from "./package.json";

data.name; // => "bun"
data.version; // => "1.0.0"
data.author.name; // => "<PERSON>"
```

---

Bun also supports [Import Attributes](https://github.com/tc39/proposal-import-attributes/) and [JSON modules](https://github.com/tc39/proposal-json-modules) syntax.

```ts
import data from "./package.json" with { type: "json" };

data.name; // => "bun"
data.version; // => "1.0.0"
data.author.name; // => "<PERSON>"
```

---

See [Docs > Runtime > TypeScript](https://bun.sh/docs/runtime/typescript) for more information on using TypeScript with Bun.
