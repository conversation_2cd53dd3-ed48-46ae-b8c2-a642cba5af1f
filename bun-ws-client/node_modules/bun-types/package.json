{"version": "1.2.18", "name": "bun-types", "license": "MIT", "types": "./index.d.ts", "description": "Type definitions and documentation for Bun, an incredibly fast JavaScript runtime", "repository": {"type": "git", "url": "https://github.com/oven-sh/bun", "directory": "packages/bun-types"}, "files": ["./*.d.ts", "docs/**/*.md", "docs/*.md", "CLAUDE.md", "README.md"], "homepage": "https://bun.sh", "dependencies": {"@types/node": "*"}, "peerDependencies": {"@types/react": "^19"}, "devDependencies": {"@types/react": "^19", "typescript": "^5.0.2"}, "scripts": {"prebuild": "echo $(pwd)", "copy-docs": "rm -rf docs && cp -rL ../../docs/ ./docs && find ./docs -type f -name '*.md' -exec sed -i 's/\\$BUN_LATEST_VERSION/'\"${BUN_VERSION#bun-v}\"'/g' {} +", "build": "bun run copy-docs && cp ../../src/init/rule.md CLAUDE.md && bun scripts/build.ts", "test": "tsc", "fmt": "echo $(which biome) && biome format --write ."}, "keywords": ["bun", "bun.js", "types"]}