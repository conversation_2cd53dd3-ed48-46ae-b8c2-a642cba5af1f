# Installation
> `npm install --save @types/bun`

# Summary
This package contains type definitions for bun (https://bun.sh).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/bun.
## [index.d.ts](https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/bun/index.d.ts)
````ts
/// <reference types="bun-types" />

````

### Additional Details
 * Last updated: Thu, 03 Jul 2025 19:02:23 GMT
 * Dependencies: [bun-types](https://npmjs.com/package/bun-types)

# Credits
These definitions were written by [<PERSON><PERSON><PERSON>](https://github.com/J<PERSON><PERSON>-<PERSON>), [<PERSON>con Partovi](https://github.com/electroid), [<PERSON><PERSON><PERSON>](https://github.com/robobun), [<PERSON>](https://github.com/dylan-conway), [<PERSON><PERSON>](https://github.com/nektro), [<PERSON>](https://github.com/RiskyMH), and [<PERSON>](https://github.com/alii).
